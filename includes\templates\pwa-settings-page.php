<?php
if (!defined('ABSPATH')) {
    exit;
}

// Check user capabilities
if (!current_user_can('manage_options')) {
    return;
}

// Get PWA status
$pwa_status = Q_PWA_Settings::get_pwa_status();
$manifest_validation = Q_PWA_Manifest::validate_manifest();
?>

<div class="wrap q-pwa-admin">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>



    <!-- Tabbed Settings Interface -->
    <div class="q-pwa-tabs-container">
        <nav class="q-pwa-tabs-nav">
            <button class="q-pwa-tab-button active" data-tab="general">
                <span class="dashicons dashicons-admin-generic"></span>
                General
            </button>
            <button class="q-pwa-tab-button" data-tab="appearance">
                <span class="dashicons dashicons-admin-appearance"></span>
                Appearance
            </button>
            <button class="q-pwa-tab-button" data-tab="offline">
                <span class="dashicons dashicons-cloud"></span>
                Offline
            </button>
            <button class="q-pwa-tab-button" data-tab="icons">
                <span class="dashicons dashicons-format-image"></span>
                Icons
            </button>
            <button class="q-pwa-tab-button" data-tab="advanced">
                <span class="dashicons dashicons-admin-tools"></span>
                Advanced
            </button>
            <button class="q-pwa-tab-button" data-tab="analytics">
                <span class="dashicons dashicons-chart-bar"></span>
                Analytics
            </button>
            <button class="q-pwa-tab-button" data-tab="status">
                <span class="dashicons dashicons-smartphone"></span>
                PWA Status
            </button>
        </nav>

        <!-- Settings Form -->
        <form action="options.php" method="post" id="q-pwa-settings-form">
            <?php settings_fields('q_pwa_settings'); ?>

            <!-- General Tab -->
            <div class="q-pwa-tab-content active" id="tab-general">
                <div class="q-pwa-tab-header">
                    <h2>General Settings</h2>
                    <p>Configure basic Progressive Web App settings for your site.</p>
                </div>

                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">Enable PWA</th>
                            <td>
                                <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_enabled', 'description' => 'Enable Progressive Web App functionality']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">App Name</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_app_name', 'description' => 'Full name of your app (max 45 characters)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Short Name</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_app_short_name', 'description' => 'Short name for home screen (max 12 characters)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Description</th>
                            <td>
                                <?php Q_PWA_Settings::render_textarea_field(['field' => 'q_pwa_app_description', 'description' => 'Brief description of your app']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Start URL</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_start_url', 'description' => 'URL to load when app is launched (relative to site root)']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Appearance Tab -->
            <div class="q-pwa-tab-content" id="tab-appearance">
                <div class="q-pwa-tab-header">
                    <h2>Appearance Settings</h2>
                    <p>Customize how your PWA looks and behaves when installed.</p>
                </div>

                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">Theme Color</th>
                            <td>
                                <?php Q_PWA_Settings::render_color_field(['field' => 'q_pwa_theme_color', 'description' => 'Primary theme color for the app']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Background Color</th>
                            <td>
                                <?php Q_PWA_Settings::render_color_field(['field' => 'q_pwa_background_color', 'description' => 'Background color for splash screen']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Display Mode</th>
                            <td>
                                <?php Q_PWA_Settings::render_select_field([
                                    'field' => 'q_pwa_display_mode',
                                    'options' => [
                                        'standalone' => 'Standalone (Recommended)',
                                        'fullscreen' => 'Fullscreen',
                                        'minimal-ui' => 'Minimal UI',
                                        'browser' => 'Browser'
                                    ],
                                    'description' => 'How the app should be displayed when launched'
                                ]); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Orientation</th>
                            <td>
                                <?php Q_PWA_Settings::render_select_field([
                                    'field' => 'q_pwa_orientation',
                                    'options' => [
                                        'any' => 'Any',
                                        'portrait' => 'Portrait',
                                        'landscape' => 'Landscape'
                                    ],
                                    'description' => 'Preferred screen orientation'
                                ]); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Offline Tab -->
            <div class="q-pwa-tab-content" id="tab-offline">
                <div class="q-pwa-tab-header">
                    <h2>Offline & Caching Settings</h2>
                    <p>Configure offline functionality and caching strategies.</p>
                </div>

                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">Enable Offline Support</th>
                            <td>
                                <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_offline_enabled', 'description' => 'Cache content for offline access']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Cache Strategy</th>
                            <td>
                                <?php Q_PWA_Settings::render_select_field([
                                    'field' => 'q_pwa_cache_strategy',
                                    'options' => [
                                        'cache_first' => 'Cache First (Faster)',
                                        'network_first' => 'Network First (Fresh Content)',
                                        'stale_while_revalidate' => 'Stale While Revalidate (Balanced)'
                                    ],
                                    'description' => 'How to handle caching for offline support'
                                ]); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Icons Tab -->
            <div class="q-pwa-tab-content" id="tab-icons">
                <div class="q-pwa-tab-header">
                    <h2>App Icons</h2>
                    <p>Upload icons for your PWA. Icons should be square and in PNG format.</p>
                </div>

                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">192x192 Icon</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_icon_192', 'description' => 'App icon for Android devices (192x192 pixels)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">512x512 Icon</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_icon_512', 'description' => 'App icon for splash screen (512x512 pixels)']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Advanced Tab -->
            <div class="q-pwa-tab-content" id="tab-advanced">
                <div class="q-pwa-tab-header">
                    <h2>Advanced Settings & Testing</h2>
                    <p>Advanced PWA configuration and testing tools.</p>
                </div>

                <div class="q-pwa-testing-tools">
                    <h3>PWA Testing Tools</h3>
                    <p>Use these tools to test your PWA configuration:</p>

                    <div class="q-pwa-tools">
                        <a href="<?php echo esc_url(Q_PWA_Manifest::get_manifest_url()); ?>" target="_blank"
                            class="button">
                            <span class="dashicons dashicons-media-code"></span>
                            View Manifest
                        </a>
                        <a href="https://web.dev/measure/" target="_blank" class="button">
                            <span class="dashicons dashicons-performance"></span>
                            Test with Lighthouse
                        </a>
                        <a href="https://manifest-validator.appspot.com/" target="_blank" class="button">
                            <span class="dashicons dashicons-yes-alt"></span>
                            Validate Manifest
                        </a>
                        <button type="button" class="button" id="q-test-manifest">
                            <span class="dashicons dashicons-search"></span>
                            Test Manifest
                        </button>
                        <button type="button" class="button" id="q-preview-pwa">
                            <span class="dashicons dashicons-external"></span>
                            Preview PWA
                        </button>
                    </div>

                    <div id="q-manifest-preview" style="display: none; margin-top: 15px;">
                        <h4>Manifest Preview</h4>
                        <pre id="q-manifest-content"
                            style="background: #f1f1f1; padding: 15px; overflow-x: auto; border-radius: 4px;"></pre>
                    </div>
                </div>

                <div class="q-pwa-installation-guide">
                    <h3>Installation Instructions</h3>
                    <p>Once your PWA is configured, users can install it on their devices:</p>

                    <div class="q-install-instructions">
                        <div class="instruction-group">
                            <h4>📱 Mobile (Android/iOS)</h4>
                            <ol>
                                <li>Open your website in Chrome (Android) or Safari (iOS)</li>
                                <li>Tap the browser menu (⋮ or share button)</li>
                                <li>Select "Add to Home Screen" or "Install App"</li>
                                <li>Follow the prompts to install</li>
                            </ol>
                        </div>

                        <div class="instruction-group">
                            <h4>💻 Desktop (Chrome/Edge)</h4>
                            <ol>
                                <li>Visit your website in Chrome or Edge</li>
                                <li>Look for the install icon in the address bar</li>
                                <li>Click "Install" when prompted</li>
                                <li>The app will be added to your applications</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Tab -->
            <div class="q-pwa-tab-content" id="tab-analytics">
                <div class="q-pwa-tab-header">
                    <h2>PWA Usage Analytics</h2>
                    <p>Track how users interact with your Progressive Web App, including installations, usage patterns,
                        and performance metrics.</p>
                </div>

                <?php
                // Get analytics data
                $analytics_summary = q_get_pwa_analytics_summary(30);
                $event_breakdown = q_get_pwa_event_breakdown(30);
                $daily_stats = q_get_pwa_daily_stats(7); // Last 7 days for chart
                $device_stats = q_get_pwa_device_stats(30);
                $performance_stats = q_get_pwa_performance_stats(30);
                $top_pages = q_get_pwa_top_pages(30, 5);
                ?>

                <!-- Analytics Dashboard -->
                <div class="q-pwa-analytics-dashboard">
                    <div class="q-analytics-cards">
                        <div class="q-analytics-card">
                            <h3><span class="dashicons dashicons-download"></span> App Installations</h3>
                            <div class="q-analytics-number">
                                <?php echo esc_html($analytics_summary['app_installations']); ?>
                            </div>
                            <div class="q-analytics-meta">
                                <?php if ($analytics_summary['install_conversion_rate'] > 0): ?>
                                    <small><?php echo esc_html($analytics_summary['install_conversion_rate']); ?>%
                                        conversion rate</small>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="q-analytics-card">
                            <h3><span class="dashicons dashicons-groups"></span> Unique Sessions</h3>
                            <div class="q-analytics-number">
                                <?php echo esc_html($analytics_summary['unique_sessions']); ?>
                            </div>
                            <div class="q-analytics-meta">
                                <small>Last 30 days</small>
                            </div>
                        </div>

                        <div class="q-analytics-card">
                            <h3><span class="dashicons dashicons-smartphone"></span> Standalone Launches</h3>
                            <div class="q-analytics-number">
                                <?php echo esc_html($analytics_summary['standalone_launches']); ?>
                            </div>
                            <div class="q-analytics-meta">
                                <small>App mode usage</small>
                            </div>
                        </div>

                        <div class="q-analytics-card">
                            <h3><span class="dashicons dashicons-cloud"></span> Offline Usage</h3>
                            <div class="q-analytics-number"><?php echo esc_html($analytics_summary['offline_usage']); ?>
                            </div>
                            <div class="q-analytics-meta">
                                <small>Offline events</small>
                            </div>
                        </div>
                    </div>

                    <!-- Event Breakdown -->
                    <div class="q-analytics-section">
                        <h3>Event Breakdown (Last 30 Days)</h3>
                        <div class="q-analytics-table-container">
                            <table class="q-analytics-table">
                                <thead>
                                    <tr>
                                        <th>Event Type</th>
                                        <th>Count</th>
                                        <th>Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $total_events = array_sum($event_breakdown);
                                    foreach ($event_breakdown as $event => $count):
                                        $percentage = $total_events > 0 ? round(($count / $total_events) * 100, 1) : 0;
                                        ?>
                                        <tr>
                                            <td><?php echo esc_html(ucwords(str_replace('_', ' ', $event))); ?></td>
                                            <td><?php echo esc_html($count); ?></td>
                                            <td><?php echo esc_html($percentage); ?>%</td>
                                        </tr>
                                    <?php endforeach; ?>
                                    <?php if (empty($event_breakdown)): ?>
                                        <tr>
                                            <td colspan="3">No analytics data available yet.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Device Stats -->
                    <?php if (!empty($device_stats)): ?>
                        <div class="q-analytics-section">
                            <h3>Device Types (Last 30 Days)</h3>
                            <div class="q-device-stats">
                                <?php foreach ($device_stats as $device => $sessions): ?>
                                    <div class="q-device-stat">
                                        <span class="device-type"><?php echo esc_html($device); ?></span>
                                        <span class="device-count"><?php echo esc_html($sessions); ?> sessions</span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Performance Stats -->
                    <?php if ($performance_stats['avg_load_time'] > 0): ?>
                        <div class="q-analytics-section">
                            <h3>Performance Metrics (Last 30 Days)</h3>
                            <div class="q-performance-stats">
                                <div class="q-perf-metric">
                                    <span class="metric-label">Average Load Time</span>
                                    <span
                                        class="metric-value"><?php echo esc_html($performance_stats['avg_load_time']); ?>ms</span>
                                </div>
                                <div class="q-perf-metric">
                                    <span class="metric-label">DOM Content Loaded</span>
                                    <span
                                        class="metric-value"><?php echo esc_html($performance_stats['avg_dom_loaded']); ?>ms</span>
                                </div>
                                <?php if ($performance_stats['avg_first_paint'] > 0): ?>
                                    <div class="q-perf-metric">
                                        <span class="metric-label">First Paint</span>
                                        <span
                                            class="metric-value"><?php echo esc_html($performance_stats['avg_first_paint']); ?>ms</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Top Pages -->
                    <?php if (!empty($top_pages)): ?>
                        <div class="q-analytics-section">
                            <h3>Most Viewed Pages (Last 30 Days)</h3>
                            <div class="q-top-pages">
                                <?php foreach ($top_pages as $page => $views): ?>
                                    <div class="q-page-stat">
                                        <span class="page-url"><?php echo esc_html($page); ?></span>
                                        <span class="page-views"><?php echo esc_html($views); ?> views</span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Analytics Controls -->
                    <div class="q-analytics-controls">
                        <button type="button" id="q-refresh-analytics" class="button button-secondary">
                            <span class="dashicons dashicons-update"></span>
                            Refresh Data
                        </button>
                        <button type="button" id="q-clear-pwa-analytics" class="button button-secondary">
                            <span class="dashicons dashicons-trash"></span>
                            Clear Analytics Data
                        </button>
                    </div>
                </div>
            </div>

            <!-- PWA Status Tab -->
            <div class="q-pwa-tab-content" id="tab-status">
                <div class="q-pwa-tab-header">
                    <h2>PWA Status</h2>
                    <p>Monitor your Progressive Web App configuration and requirements.</p>
                </div>

                <!-- PWA Status Dashboard -->
                <div class="q-pwa-status-card">
                    <div class="status-header">
                        <h2><span class="dashicons dashicons-smartphone"></span> PWA Status</h2>
                        <div class="status-badge <?php echo $pwa_status['ready'] ? 'ready' : 'incomplete'; ?>">
                            <?php echo $pwa_status['ready'] ? 'Ready' : 'Incomplete'; ?>
                        </div>
                    </div>
                    <div class="q-pwa-progress">
                        <div class="progress-bar">
                            <div class="progress-fill"
                                style="width: <?php echo esc_attr($pwa_status['percentage']); ?>%"></div>
                        </div>
                        <p class="progress-text"><?php echo esc_html($pwa_status['percentage']); ?>% Complete</p>
                    </div>

                    <div class="q-pwa-requirements">
                        <h3>Requirements Checklist</h3>
                        <ul>
                            <li class="<?php echo $pwa_status['requirements']['https'] ? 'completed' : 'pending'; ?>">
                                <span
                                    class="dashicons <?php echo $pwa_status['requirements']['https'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                                HTTPS Enabled
                                <?php if (!$pwa_status['requirements']['https']): ?>
                                    <small>Your site must use HTTPS for PWA functionality</small>
                                <?php endif; ?>
                            </li>
                            <li
                                class="<?php echo $pwa_status['requirements']['manifest'] ? 'completed' : 'pending'; ?>">
                                <span
                                    class="dashicons <?php echo $pwa_status['requirements']['manifest'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                                PWA Enabled
                                <?php if (!$pwa_status['requirements']['manifest']): ?>
                                    <small>Enable PWA functionality in the General tab</small>
                                <?php endif; ?>
                            </li>
                            <li
                                class="<?php echo $pwa_status['requirements']['service_worker'] ? 'completed' : 'pending'; ?>">
                                <span
                                    class="dashicons <?php echo $pwa_status['requirements']['service_worker'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                                Service Worker Installed
                                <?php if (!$pwa_status['requirements']['service_worker']): ?>
                                    <small>Service worker is required for offline functionality</small>
                                <?php endif; ?>
                            </li>
                            <li class="<?php echo $pwa_status['requirements']['icons'] ? 'completed' : 'pending'; ?>">
                                <span
                                    class="dashicons <?php echo $pwa_status['requirements']['icons'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                                App Icons Configured
                                <?php if (!$pwa_status['requirements']['icons']): ?>
                                    <small>Upload 192x192 and 512x512 icons in the Icons tab</small>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>

                    <?php if ($pwa_status['ready']): ?>
                        <div class="notice notice-success inline">
                            <p><strong>🎉 Your PWA is ready!</strong> Users can now install your app on their devices.</p>
                        </div>
                    <?php else: ?>
                        <div class="notice notice-warning inline">
                            <p><strong>⚠️ PWA Setup Incomplete</strong> Complete the requirements above to enable full PWA
                                functionality.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Manifest Validation -->
                <?php if (!$manifest_validation['valid']): ?>
                    <div class="notice notice-error">
                        <h3><span class="dashicons dashicons-warning"></span> Manifest Validation Errors</h3>
                        <ul>
                            <?php foreach ($manifest_validation['errors'] as $error): ?>
                                <li><?php echo esc_html($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Save Button -->
            <div class="q-pwa-actions">
                <?php submit_button('Save PWA Settings', 'primary', 'submit', false); ?>
            </div>
        </form>
    </div>
</div>

<script>
    // Fallback tab functionality in case external JS doesn't load
    jQuery(document).ready(function ($) {
        console.log('PWA Admin: Initializing tab functionality');

        // Simple tab switching
        $('.q-pwa-tab-button').on('click', function (e) {
            e.preventDefault();
            console.log('PWA Admin: Tab clicked');

            const tabId = $(this).data('tab');
            console.log('PWA Admin: Switching to tab:', tabId);

            // Remove active class from all tabs and buttons
            $('.q-pwa-tab-button').removeClass('active');
            $('.q-pwa-tab-content').removeClass('active');

            // Add active class to clicked button and corresponding tab
            $(this).addClass('active');
            $('#tab-' + tabId).addClass('active');

            console.log('PWA Admin: Tab switched successfully');
        });

        // Test if elements exist
        console.log('PWA Admin: Tab buttons found:', $('.q-pwa-tab-button').length);
        console.log('PWA Admin: Tab contents found:', $('.q-pwa-tab-content').length);
        console.log('PWA Admin: jQuery version:', $.fn.jquery);
        console.log('PWA Admin: qPWAAdmin object:', typeof qPWAAdmin !== 'undefined' ? qPWAAdmin : 'undefined');

        // List all tab buttons and their data-tab values
        $('.q-pwa-tab-button').each(function (index) {
            console.log('PWA Admin: Tab button', index, '- data-tab:', $(this).data('tab'), '- text:', $(this).text().trim());
        });

        // List all tab contents and their IDs
        $('.q-pwa-tab-content').each(function (index) {
            console.log('PWA Admin: Tab content', index, '- ID:', $(this).attr('id'), '- classes:', $(this).attr('class'));
        });

        // Analytics controls are now handled in pwa-admin.js
    });
</script>

<style>
    /* Fallback styles for tabs */
    .q-pwa-tabs-container {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        margin-top: 20px;
    }

    .q-pwa-tabs-nav {
        display: flex;
        background: #f6f7f7;
        border-bottom: 1px solid #ccd0d4;
        margin: 0;
        padding: 0;
    }

    .q-pwa-tab-button {
        background: none;
        border: none;
        padding: 12px 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        color: #50575e;
        transition: all 0.2s ease;
        border-bottom: 3px solid transparent;
    }

    .q-pwa-tab-button:hover {
        background: #fff;
        color: #1d2327;
    }

    .q-pwa-tab-button.active {
        background: #fff;
        color: #2271b1;
        border-bottom-color: #2271b1;
    }

    .q-pwa-tab-content {
        display: none;
        padding: 20px;
    }

    .q-pwa-tab-content.active {
        display: block;
    }

    .q-pwa-tab-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #f1f1f1;
    }

    .q-pwa-tab-header h2 {
        margin: 0 0 8px 0;
        color: #1d2327;
    }

    .q-pwa-tab-header p {
        margin: 0;
        color: #646970;
    }

    .q-pwa-actions {
        margin: 20px 0 0 0;
        padding: 15px 20px;
        background: #f6f7f7;
        border-top: 1px solid #ccd0d4;
    }

    /* Analytics styles are now in pwa-admin.css */
</style>