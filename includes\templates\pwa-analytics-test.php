<?php
if (!defined('ABSPATH')) {
    die('You are not allowed to call this page directly.');
}

// This is a test page to demonstrate PWA analytics functionality
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Analytics Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f9f9f9;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #2271b1;
        }
        .test-button {
            background: #2271b1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #1a5a8a;
        }
        .analytics-output {
            background: #fff;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>PWA Analytics Test Page</h1>
    <p>This page demonstrates the PWA analytics functionality. Use the buttons below to trigger various analytics events and see how they're tracked.</p>

    <div class="test-section">
        <h2>Manual Event Tracking</h2>
        <p>Click these buttons to manually trigger PWA analytics events:</p>
        
        <button class="test-button" onclick="testAppLaunch()">Test App Launch</button>
        <button class="test-button" onclick="testPageView()">Test Page View</button>
        <button class="test-button" onclick="testInstallPrompt()">Test Install Prompt</button>
        <button class="test-button" onclick="testOfflineEvent()">Test Offline Event</button>
        <button class="test-button" onclick="testCustomEvent()">Test Custom Event</button>
        
        <div id="event-status"></div>
    </div>

    <div class="test-section">
        <h2>Analytics Data Viewer</h2>
        <p>View current analytics data from the database:</p>
        
        <button class="test-button" onclick="loadAnalyticsData()">Load Analytics Summary</button>
        <button class="test-button" onclick="loadEventBreakdown()">Load Event Breakdown</button>
        <button class="test-button" onclick="loadRecentEvents()">Load Recent Events</button>
        
        <div class="analytics-output" id="analytics-output">Click a button above to load analytics data...</div>
    </div>

    <div class="test-section">
        <h2>PWA Status Check</h2>
        <p>Check current PWA status and capabilities:</p>
        
        <button class="test-button" onclick="checkPWAStatus()">Check PWA Status</button>
        <button class="test-button" onclick="checkServiceWorker()">Check Service Worker</button>
        <button class="test-button" onclick="checkInstallability()">Check Installability</button>
        
        <div class="analytics-output" id="pwa-status">Click a button above to check PWA status...</div>
    </div>

    <script>
        // Test functions for manual event tracking
        function testAppLaunch() {
            if (window.qPWAManager) {
                window.qPWAManager.trackAppLaunch();
                showStatus('App launch event tracked!', 'success');
            } else {
                showStatus('PWA Manager not available', 'error');
            }
        }

        function testPageView() {
            if (window.qPWAManager) {
                window.qPWAManager.trackPageView();
                showStatus('Page view event tracked!', 'success');
            } else {
                showStatus('PWA Manager not available', 'error');
            }
        }

        function testInstallPrompt() {
            if (window.qPWAManager) {
                window.qPWAManager.trackEvent('install_prompt_test', {
                    test: true,
                    timestamp: new Date().toISOString()
                });
                showStatus('Install prompt test event tracked!', 'success');
            } else {
                showStatus('PWA Manager not available', 'error');
            }
        }

        function testOfflineEvent() {
            if (window.qPWAManager) {
                window.qPWAManager.trackEvent('offline_test', {
                    test: true,
                    isOnline: navigator.onLine
                });
                showStatus('Offline test event tracked!', 'success');
            } else {
                showStatus('PWA Manager not available', 'error');
            }
        }

        function testCustomEvent() {
            if (window.qPWAManager) {
                window.qPWAManager.trackEvent('custom_test_event', {
                    test: true,
                    customData: 'This is a test event',
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent
                });
                showStatus('Custom test event tracked!', 'success');
            } else {
                showStatus('PWA Manager not available', 'error');
            }
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('event-status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 3000);
        }

        // Analytics data loading functions
        function loadAnalyticsData() {
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'q_get_pwa_analytics_summary',
                    nonce: '<?php echo wp_create_nonce('q_pwa_test_nonce'); ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('analytics-output').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('analytics-output').textContent = 'Error loading analytics data: ' + error.message;
            });
        }

        function loadEventBreakdown() {
            // This would need a corresponding AJAX handler
            document.getElementById('analytics-output').textContent = 'Event breakdown loading... (requires AJAX handler implementation)';
        }

        function loadRecentEvents() {
            // This would show recent events from the database
            document.getElementById('analytics-output').textContent = 'Recent events loading... (requires AJAX handler implementation)';
        }

        // PWA status check functions
        function checkPWAStatus() {
            const status = {
                isStandalone: window.matchMedia('(display-mode: standalone)').matches,
                isOnline: navigator.onLine,
                hasServiceWorker: 'serviceWorker' in navigator,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            };
            
            document.getElementById('pwa-status').textContent = JSON.stringify(status, null, 2);
        }

        function checkServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(registrations => {
                    const swStatus = {
                        registrations: registrations.length,
                        active: registrations.map(reg => ({
                            scope: reg.scope,
                            state: reg.active ? reg.active.state : 'none'
                        }))
                    };
                    document.getElementById('pwa-status').textContent = JSON.stringify(swStatus, null, 2);
                });
            } else {
                document.getElementById('pwa-status').textContent = 'Service Worker not supported';
            }
        }

        function checkInstallability() {
            const installStatus = {
                beforeInstallPromptSupported: 'onbeforeinstallprompt' in window,
                isInstalled: window.matchMedia('(display-mode: standalone)').matches,
                platform: navigator.platform,
                userAgent: navigator.userAgent
            };
            
            document.getElementById('pwa-status').textContent = JSON.stringify(installStatus, null, 2);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PWA Analytics Test Page loaded');
            
            // Check if PWA Manager is available
            if (window.qPWAManager) {
                console.log('PWA Manager is available');
                // Track that the test page was loaded
                window.qPWAManager.trackEvent('test_page_loaded');
            } else {
                console.log('PWA Manager not available');
            }
        });
    </script>
</body>
</html>
