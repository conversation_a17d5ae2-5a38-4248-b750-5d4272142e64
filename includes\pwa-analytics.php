<?php
if (!defined('ABSPATH')) {
    die('You are not allowed to call this page directly.');
}

/**
 * PWA Analytics Functions
 * Handles analytics data retrieval and processing for PWA usage
 */

if (!function_exists('q_get_pwa_analytics_summary')) {
    function q_get_pwa_analytics_summary($days = 30)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        
        $date_limit = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        // Get total events
        $total_events = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE timestamp >= %s",
            $date_limit
        ));
        
        // Get unique users
        $unique_users = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT user_id) FROM $table_name WHERE timestamp >= %s AND user_id > 0",
            $date_limit
        ));
        
        // Get unique sessions
        $unique_sessions = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT JSON_EXTRACT(event_data, '$.sessionId')) FROM $table_name 
             WHERE timestamp >= %s AND JSON_EXTRACT(event_data, '$.sessionId') IS NOT NULL",
            $date_limit
        ));
        
        // Get app installations
        $app_installations = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name 
             WHERE event_name = 'app_installed' AND timestamp >= %s",
            $date_limit
        ));
        
        // Get install prompts shown
        $install_prompts = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name 
             WHERE event_name = 'install_prompt_shown' AND timestamp >= %s",
            $date_limit
        ));
        
        // Calculate install conversion rate
        $install_conversion_rate = $install_prompts > 0 ? round(($app_installations / $install_prompts) * 100, 1) : 0;
        
        // Get standalone app launches
        $standalone_launches = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name 
             WHERE event_name = 'app_launch' 
             AND JSON_EXTRACT(event_data, '$.launchMode') = 'standalone'
             AND timestamp >= %s",
            $date_limit
        ));
        
        // Get offline usage events
        $offline_usage = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name 
             WHERE event_name = 'connection_offline' AND timestamp >= %s",
            $date_limit
        ));
        
        return [
            'total_events' => intval($total_events),
            'unique_users' => intval($unique_users),
            'unique_sessions' => intval($unique_sessions),
            'app_installations' => intval($app_installations),
            'install_prompts' => intval($install_prompts),
            'install_conversion_rate' => $install_conversion_rate,
            'standalone_launches' => intval($standalone_launches),
            'offline_usage' => intval($offline_usage),
            'period_days' => $days
        ];
    }
}

if (!function_exists('q_get_pwa_event_breakdown')) {
    function q_get_pwa_event_breakdown($days = 30)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        
        $date_limit = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT event_name, COUNT(*) as count 
             FROM $table_name 
             WHERE timestamp >= %s 
             GROUP BY event_name 
             ORDER BY count DESC",
            $date_limit
        ));
        
        $breakdown = [];
        foreach ($results as $result) {
            $breakdown[$result->event_name] = intval($result->count);
        }
        
        return $breakdown;
    }
}

if (!function_exists('q_get_pwa_daily_stats')) {
    function q_get_pwa_daily_stats($days = 30)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        
        $date_limit = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT 
                DATE(timestamp) as date,
                COUNT(*) as total_events,
                COUNT(DISTINCT JSON_EXTRACT(event_data, '$.sessionId')) as unique_sessions,
                SUM(CASE WHEN event_name = 'app_installed' THEN 1 ELSE 0 END) as installations,
                SUM(CASE WHEN event_name = 'app_launch' AND JSON_EXTRACT(event_data, '$.launchMode') = 'standalone' THEN 1 ELSE 0 END) as standalone_launches
             FROM $table_name 
             WHERE timestamp >= %s 
             GROUP BY DATE(timestamp) 
             ORDER BY date DESC",
            $date_limit
        ));
        
        $daily_stats = [];
        foreach ($results as $result) {
            $daily_stats[] = [
                'date' => $result->date,
                'total_events' => intval($result->total_events),
                'unique_sessions' => intval($result->unique_sessions),
                'installations' => intval($result->installations),
                'standalone_launches' => intval($result->standalone_launches)
            ];
        }
        
        return $daily_stats;
    }
}

if (!function_exists('q_get_pwa_device_stats')) {
    function q_get_pwa_device_stats($days = 30)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        
        $date_limit = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        // Get device types based on user agent
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT 
                CASE 
                    WHEN JSON_EXTRACT(event_data, '$.userAgent') LIKE '%%Mobile%%' THEN 'Mobile'
                    WHEN JSON_EXTRACT(event_data, '$.userAgent') LIKE '%%Tablet%%' THEN 'Tablet'
                    ELSE 'Desktop'
                END as device_type,
                COUNT(DISTINCT JSON_EXTRACT(event_data, '$.sessionId')) as sessions
             FROM $table_name 
             WHERE timestamp >= %s 
             AND JSON_EXTRACT(event_data, '$.sessionId') IS NOT NULL
             GROUP BY device_type",
            $date_limit
        ));
        
        $device_stats = [];
        foreach ($results as $result) {
            $device_stats[$result->device_type] = intval($result->sessions);
        }
        
        return $device_stats;
    }
}

if (!function_exists('q_get_pwa_performance_stats')) {
    function q_get_pwa_performance_stats($days = 30)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        
        $date_limit = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT 
                AVG(CAST(JSON_EXTRACT(event_data, '$.loadTime') AS DECIMAL(10,2))) as avg_load_time,
                AVG(CAST(JSON_EXTRACT(event_data, '$.domContentLoaded') AS DECIMAL(10,2))) as avg_dom_loaded,
                AVG(CAST(JSON_EXTRACT(event_data, '$.firstPaint') AS DECIMAL(10,2))) as avg_first_paint
             FROM $table_name 
             WHERE event_name = 'performance_metrics' 
             AND timestamp >= %s
             AND JSON_EXTRACT(event_data, '$.loadTime') IS NOT NULL",
            $date_limit
        ));
        
        if (empty($results) || !$results[0]->avg_load_time) {
            return [
                'avg_load_time' => 0,
                'avg_dom_loaded' => 0,
                'avg_first_paint' => 0
            ];
        }
        
        return [
            'avg_load_time' => round(floatval($results[0]->avg_load_time), 2),
            'avg_dom_loaded' => round(floatval($results[0]->avg_dom_loaded), 2),
            'avg_first_paint' => round(floatval($results[0]->avg_first_paint), 2)
        ];
    }
}

if (!function_exists('q_get_pwa_top_pages')) {
    function q_get_pwa_top_pages($days = 30, $limit = 10)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        
        $date_limit = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT 
                JSON_EXTRACT(event_data, '$.page') as page,
                COUNT(*) as views
             FROM $table_name 
             WHERE event_name = 'page_view' 
             AND timestamp >= %s
             AND JSON_EXTRACT(event_data, '$.page') IS NOT NULL
             GROUP BY page 
             ORDER BY views DESC 
             LIMIT %d",
            $date_limit,
            $limit
        ));
        
        $top_pages = [];
        foreach ($results as $result) {
            $page = trim($result->page, '"'); // Remove JSON quotes
            $top_pages[$page] = intval($result->views);
        }
        
        return $top_pages;
    }
}

if (!function_exists('q_clear_pwa_analytics')) {
    function q_clear_pwa_analytics()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        
        $result = $wpdb->query("TRUNCATE TABLE $table_name");
        
        return $result !== false;
    }
}

// AJAX handler for clearing PWA analytics
add_action('wp_ajax_q_clear_pwa_analytics', 'q_clear_pwa_analytics_callback');

function q_clear_pwa_analytics_callback()
{
    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error(__('Permission denied.', 'q-pusher-q-pwa'));
        return;
    }

    // Verify nonce
    check_ajax_referer('q_pwa_admin_nonce', 'nonce');

    if (q_clear_pwa_analytics()) {
        wp_send_json_success(__('PWA analytics data cleared successfully.', 'q-pusher-q-pwa'));
    } else {
        wp_send_json_error(__('Failed to clear PWA analytics data.', 'q-pusher-q-pwa'));
    }
}
